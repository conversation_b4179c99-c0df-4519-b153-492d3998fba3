import React, { useEffect, useRef } from "react";
import { Github, ExternalLink, Play, FileText } from "lucide-react";
import { Card, CardContent } from "./ui/card";
import { Link } from "react-router-dom";

const Projects = () => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const projects = [
    {
      id: "heart-attack-app",
      title: "Heart Attack App",
      description:
        "A medical application designed to enhance the customer experience for healthcare monitoring. Transformed a poorly functioning app into a stable, professional mobile application with health tracking modules.",
      image:
        "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
      technologies: ["Flutter", "React Native", "Firebase", "SQLite"],
      achievements: [
        "Resolved 50+ critical bugs",
        "Enhanced UI/UX with smooth animations",
        "90% decrease in crash reports",
        "Integrated MedTech standards",
      ],
      demoUrl:
        "https://play.google.com/store/apps/details?id=com.heartattack.app",
      codeUrl: null,
      status: "Live",
    },

    {
      id: "uafa-mailbox",
      title: "UAFAMailBox",
      description:
        "A secure document and correspondence management system for institutions with electronic archiving, communication tracking, and role-based access control.",
      image:
        "https://images.unsplash.com/photo-**********-6726b3ff858f?w=600&h=400&fit=crop",
      technologies: ["Flutter", "SQLite", "Firebase", "Bluetooth"],
      achievements: [
        "Robust database architecture",
        "Bluetooth device integration",
        "Responsive UI components",
        "End-to-end project ownership",
      ],
      demoUrl: null,
      codeUrl: "https://github.com/abdulrahman/uafa-mailbox",
      status: "Private",
    },
  ];

  const handleDemoClick = (project: any) => {
    if (!project.demoUrl) {
      alert(
        `Demo not available for ${
          project.title
        }. This is a ${project.status.toLowerCase()} project.`
      );
      return;
    }
    window.open(project.demoUrl, "_blank", "noopener,noreferrer");
  };

  const handleCodeClick = (project: any) => {
    if (!project.codeUrl) {
      alert(
        `Source code not available for ${project.title}. This is proprietary/private code.`
      );
      return;
    }
    window.open(project.codeUrl, "_blank", "noopener,noreferrer");
  };

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      Live: "bg-green-100 text-green-800 border border-green-200",
      Demo: "bg-blue-100 text-blue-800 border border-blue-200",
      Private: "bg-gray-100 text-gray-800 border border-gray-200",
    };

    return (
      <span
        className={`px-3 py-1 rounded-full text-xs font-semibold ${
          statusStyles[status as keyof typeof statusStyles]
        }`}
      >
        {status}
      </span>
    );
  };

  return (
    <section
      ref={sectionRef}
      id="projects"
      className="py-24 bg-gradient-to-br from-gray-50 to-white"
    >
      <div className="container mx-auto px-6">
        <div className="text-center mb-20 scroll-reveal">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Play size={16} />
            Portfolio Showcase
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Featured <span className="text-gradient">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore my portfolio of mobile applications that showcase technical
            expertise, innovative solutions, and measurable impact across
            various industries.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {projects.map((project, index) => (
            <Card
              key={index}
              className="scroll-reveal group overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div className="absolute top-4 right-4">
                  {getStatusBadge(project.status)}
                </div>
                <div className="absolute bottom-4 left-4">
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {project.title}
                  </h3>
                </div>
              </div>

              <CardContent className="p-8">
                <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                  {project.description}
                </p>

                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <div className="w-2 h-2 gradient-primary rounded-full"></div>
                    Key Achievements
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {project.achievements.map((achievement, i) => (
                      <div key={i} className="flex items-start gap-3">
                        <div className="w-1.5 h-1.5 gradient-primary rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600 text-sm leading-relaxed">
                          {achievement}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-8">
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, i) => (
                      <span
                        key={i}
                        className="px-4 py-2 bg-gradient-to-r from-purple-50 to-blue-50 text-gray-700 rounded-full text-sm font-medium border border-purple-100 hover:border-purple-200 transition-colors"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={() => handleDemoClick(project)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                      project.demoUrl
                        ? "gradient-primary text-white shadow-lg hover:shadow-xl"
                        : "bg-gray-100 text-gray-400 cursor-not-allowed"
                    }`}
                    disabled={!project.demoUrl}
                  >
                    <ExternalLink size={16} />
                    {project.demoUrl ? "View Demo" : "Demo N/A"}
                  </button>

                  <Link
                    to={`/project/${project.id}`}
                    className="flex items-center gap-2 px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:border-gray-400 hover:bg-gray-50"
                  >
                    <FileText size={16} />
                    View Details
                  </Link>

                  <button
                    onClick={() => handleCodeClick(project)}
                    className={`flex items-center gap-2 px-6 py-3 border-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                      project.codeUrl
                        ? "border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50"
                        : "border-gray-200 text-gray-400 cursor-not-allowed"
                    }`}
                    disabled={!project.codeUrl}
                  >
                    <Github size={16} />
                    {project.codeUrl ? "Code" : "Private"}
                  </button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
