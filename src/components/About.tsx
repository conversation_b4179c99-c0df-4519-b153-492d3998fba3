import React, { useEffect, useRef } from "react";
import {
  Code,
  Users,
  Star,
  Award,
  Zap,
  Heart,
  Target,
  Download,
  Mail,
  MapPin,
  Calendar,
  Smartphone,
  Palette,
  Rocket,
  Sparkles,
  Coffee,
  Monitor,
} from "lucide-react";

const About = () => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const stats = [
    {
      icon: Code,
      value: "4+",
      label: "Years Experience",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Star,
      value: "7",
      label: "Freelance Projects",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: Users,
      value: "100%",
      label: "Client Satisfaction",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: Award,
      value: "5.0",
      label: "Average Rating",
      color: "from-purple-500 to-pink-500",
    },
  ];

  const highlights = [
    {
      icon: Zap,
      title: "Performance Focused",
      description: "Optimizing apps for speed and efficiency",
      color: "from-yellow-400 to-orange-400",
    },
    {
      icon: Heart,
      title: "User-Centric Design",
      description: "Creating intuitive and engaging experiences",
      color: "from-pink-400 to-red-400",
    },
    {
      icon: Target,
      title: "Problem Solver",
      description: "Turning complex challenges into simple solutions",
      color: "from-blue-400 to-indigo-400",
    },
  ];

  return (
    <section
      ref={sectionRef}
      id="about"
      className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full -translate-x-1/2 -translate-y-1/2 opacity-50"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-cyan-100 to-purple-100 rounded-full translate-x-1/2 translate-y-1/2 opacity-50"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-20 scroll-reveal">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Heart size={16} />
            About Me
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Crafting Digital <span className="text-gradient">Experiences</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Passionate mobile developer dedicated to creating exceptional user
            experiences through innovative technology and clean, efficient code.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          <div className="scroll-reveal">
            <div className="relative">
              {/* Main Icon Display */}
              <div className="relative bg-gradient-to-br from-purple-50 to-blue-50 rounded-3xl p-12 border border-purple-100">
                {/* Floating Icons */}
                <div className="absolute top-6 right-6 w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg animate-bounce">
                  <Smartphone className="text-white" size={28} />
                </div>

                <div className="absolute bottom-6 left-6 w-14 h-14 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-xl flex items-center justify-center shadow-lg animate-pulse">
                  <Palette className="text-white" size={24} />
                </div>

                <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-gradient-to-br from-orange-400 to-red-400 rounded-full flex items-center justify-center shadow-lg animate-ping">
                  <Rocket className="text-white" size={20} />
                </div>

                {/* Central Avatar */}
                <div className="flex flex-col items-center text-center">
                  <div className="relative mb-6">
                    <div className="w-32 h-32 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center shadow-2xl">
                      <Code className="text-white" size={48} />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                      <Sparkles className="text-white" size={16} />
                    </div>
                  </div>

                  <h4 className="text-2xl font-bold text-gray-900 mb-2">
                    Abdul-Rahman Hany
                  </h4>
                  <p className="text-purple-600 font-semibold mb-4">
                    Mobile App Developer
                  </p>

                  {/* Tech Stack Icons */}
                  <div className="flex gap-3">
                    <div className="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center border border-gray-200">
                      <Monitor className="text-blue-600" size={20} />
                    </div>
                    <div className="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center border border-gray-200">
                      <Coffee className="text-orange-600" size={20} />
                    </div>
                    <div className="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center border border-gray-200">
                      <Heart className="text-red-500" size={20} />
                    </div>
                  </div>
                </div>
              </div>

              {/* Background Decorations */}
              <div className="absolute -z-10 top-4 left-4 w-full h-full bg-gradient-to-br from-purple-200 to-blue-200 rounded-3xl opacity-30"></div>
              <div className="absolute -z-20 top-8 left-8 w-full h-full bg-gradient-to-br from-cyan-200 to-purple-200 rounded-3xl opacity-20"></div>
            </div>
          </div>

          <div className="scroll-reveal space-y-8">
            <div>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-sm font-medium mb-6">
                <Sparkles size={16} />
                Mobile Developer
              </div>

              <h3 className="text-5xl font-bold text-gray-900 mb-8 leading-tight">
                Building the <span className="text-gradient">Future</span> of
                Mobile
              </h3>

              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border-l-4 border-purple-500">
                  <p className="text-xl font-medium text-gray-800 mb-2">
                    🚀 Transforming Ideas into Reality
                  </p>
                  <p>
                    With <strong className="text-purple-600">4+ years</strong>{" "}
                    of experience, I craft exceptional mobile experiences using{" "}
                    <strong className="text-blue-600">Flutter</strong> and
                    <strong className="text-cyan-600"> React Native</strong>.
                    Every line of code is written with performance, scalability,
                    and user delight in mind.
                  </p>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-6 border-l-4 border-blue-500">
                  <p className="text-xl font-medium text-gray-800 mb-2">
                    🎓 Strong Technical Foundation
                  </p>
                  <p>
                    <strong className="text-gray-800">
                      B.S. in Computer Science
                    </strong>{" "}
                    from ELShourok Academy (2023) equipped me with deep
                    knowledge in algorithms, data structures, and software
                    engineering principles that I apply to solve complex
                    challenges.
                  </p>
                </div>

                <div className="bg-gradient-to-r from-cyan-50 to-purple-50 rounded-2xl p-6 border-l-4 border-cyan-500">
                  <p className="text-xl font-medium text-gray-800 mb-2">
                    💡 Mission-Driven Development
                  </p>
                  <p>
                    I believe in creating mobile solutions that don't just
                    function—they inspire. My goal is to bridge complex business
                    needs with intuitive user experiences, delivering apps that
                    users love and businesses depend on.
                  </p>
                </div>
              </div>
            </div>

            {/* Enhanced Info Grid */}
            <div className="grid grid-cols-2 gap-6">
              <div className="group bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <MapPin className="text-white" size={20} />
                  </div>
                  <div>
                    <p className="font-bold text-gray-900 text-lg">Based in</p>
                    <p className="text-purple-600 font-semibold">Egypt 🇪🇬</p>
                  </div>
                </div>
              </div>

              <div className="group bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Calendar className="text-white" size={20} />
                  </div>
                  <div>
                    <p className="font-bold text-gray-900 text-lg">Status</p>
                    <p className="text-green-600 font-semibold">Available ✨</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modern CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <a
                href="#contact"
                className="group relative inline-flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 text-white rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-700 via-blue-700 to-cyan-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Mail
                  className="relative z-10 group-hover:rotate-12 transition-transform duration-300"
                  size={22}
                />
                <span className="relative z-10">
                  Let's Build Something Amazing
                </span>
              </a>

              <a
                href="/resume.pdf"
                target="_blank"
                className="group inline-flex items-center justify-center gap-3 px-8 py-4 bg-white border-2 border-gray-300 text-gray-700 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:border-purple-400 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:text-purple-700 shadow-lg hover:shadow-xl"
              >
                <Download className="group-hover:animate-bounce" size={22} />
                <span>Download Resume</span>
              </a>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="scroll-reveal">
          <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center group">
                    <div
                      className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                    >
                      <IconComponent className="text-white" size={28} />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {stat.value}
                    </div>
                    <div className="text-gray-600 font-medium">
                      {stat.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Highlights Section */}
        <div className="scroll-reveal mt-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            What Makes Me <span className="text-gradient">Different</span>
          </h3>

          <div className="grid md:grid-cols-3 gap-8">
            {highlights.map((highlight, index) => {
              const IconComponent = highlight.icon;
              return (
                <div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
                >
                  <div
                    className={`inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br ${highlight.color} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="text-white" size={24} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">
                    {highlight.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    {highlight.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
