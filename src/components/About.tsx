import React, { useEffect, useRef } from "react";
import {
  Code,
  Users,
  Star,
  Award,
  Zap,
  Heart,
  Target,
  Download,
  Mail,
  MapPin,
  Calendar,
} from "lucide-react";

const About = () => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const stats = [
    {
      icon: Code,
      value: "4+",
      label: "Years Experience",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Star,
      value: "7",
      label: "Upwork Projects",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: Users,
      value: "100%",
      label: "Client Satisfaction",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: Award,
      value: "5.0",
      label: "Average Rating",
      color: "from-purple-500 to-pink-500",
    },
  ];

  const highlights = [
    {
      icon: Zap,
      title: "Performance Focused",
      description: "Optimizing apps for speed and efficiency",
      color: "from-yellow-400 to-orange-400",
    },
    {
      icon: Heart,
      title: "User-Centric Design",
      description: "Creating intuitive and engaging experiences",
      color: "from-pink-400 to-red-400",
    },
    {
      icon: Target,
      title: "Problem Solver",
      description: "Turning complex challenges into simple solutions",
      color: "from-blue-400 to-indigo-400",
    },
  ];

  return (
    <section
      ref={sectionRef}
      id="about"
      className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full -translate-x-1/2 -translate-y-1/2 opacity-50"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-cyan-100 to-purple-100 rounded-full translate-x-1/2 translate-y-1/2 opacity-50"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-20 scroll-reveal">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Heart size={16} />
            About Me
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Crafting Digital <span className="text-gradient">Experiences</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Passionate mobile developer dedicated to creating exceptional user
            experiences through innovative technology and clean, efficient code.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          <div className="scroll-reveal">
            <div className="relative group">
              <div className="absolute -inset-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-3xl opacity-20 group-hover:opacity-30 transition-opacity duration-300 blur-xl"></div>
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=800&fit=crop"
                alt="Abdul-Rahman Hany - Mobile Developer"
                className="relative w-full rounded-3xl shadow-2xl transform group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl -z-10 opacity-80"></div>
              <div className="absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-full -z-10 opacity-60"></div>
            </div>
          </div>

          <div className="scroll-reveal space-y-8">
            <div>
              <h3 className="text-4xl font-bold text-gray-900 mb-6">
                Hi, I'm <span className="text-gradient">Abdul-Rahman</span> 👋
              </h3>

              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  A passionate{" "}
                  <strong className="text-gray-800">
                    Mobile App Developer
                  </strong>{" "}
                  with 4+ years of experience transforming ideas into powerful,
                  user-friendly mobile applications. I specialize in
                  <strong className="text-purple-600"> Flutter</strong> and{" "}
                  <strong className="text-blue-600">React Native</strong>
                  development.
                </p>

                <p>
                  I graduated with a{" "}
                  <strong className="text-gray-800">
                    B.S. in Computer Science
                  </strong>{" "}
                  from ELShourok Academy in 2023, building a solid foundation in
                  algorithms, data structures, and software engineering
                  principles.
                </p>

                <p>
                  My mission is to bridge the gap between complex business
                  requirements and intuitive user experiences, creating mobile
                  solutions that not only work flawlessly but also delight users
                  every step of the way.
                </p>
              </div>
            </div>

            {/* Quick Info Cards */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-3">
                  <MapPin className="text-purple-600" size={20} />
                  <div>
                    <p className="font-semibold text-gray-900">Location</p>
                    <p className="text-sm text-gray-600">Egypt</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-3">
                  <Calendar className="text-blue-600" size={20} />
                  <div>
                    <p className="font-semibold text-gray-900">Available</p>
                    <p className="text-sm text-gray-600">For Projects</p>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-wrap gap-4 pt-4">
              <a
                href="#contact"
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <Mail size={20} />
                Let's Work Together
              </a>
              <a
                href="/resume.pdf"
                target="_blank"
                className="inline-flex items-center gap-2 px-8 py-4 bg-white border-2 border-gray-300 text-gray-700 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 hover:border-gray-400 hover:bg-gray-50"
              >
                <Download size={20} />
                Download CV
              </a>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="scroll-reveal">
          <div className="bg-white/60 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center group">
                    <div
                      className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                    >
                      <IconComponent className="text-white" size={28} />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {stat.value}
                    </div>
                    <div className="text-gray-600 font-medium">
                      {stat.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Highlights Section */}
        <div className="scroll-reveal mt-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            What Makes Me <span className="text-gradient">Different</span>
          </h3>

          <div className="grid md:grid-cols-3 gap-8">
            {highlights.map((highlight, index) => {
              const IconComponent = highlight.icon;
              return (
                <div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
                >
                  <div
                    className={`inline-flex items-center justify-center w-14 h-14 bg-gradient-to-br ${highlight.color} rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="text-white" size={24} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">
                    {highlight.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    {highlight.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
