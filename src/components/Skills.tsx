import React, { useEffect, useRef } from "react";
import { Code2, Shield, Globe, Wrench } from "lucide-react";

const Skills = () => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const allSkills = [
    {
      name: "Dart",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/dart/dart-original.svg",
      color: "from-blue-500 to-cyan-500",
    },
    {
      name: "JavaScript",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/javascript/javascript-original.svg",
      color: "from-yellow-500 to-orange-500",
    },
    {
      name: "Java",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/java/java-original.svg",
      color: "from-red-500 to-pink-500",
    },
    {
      name: "Kotlin",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/kotlin/kotlin-original.svg",
      color: "from-purple-500 to-indigo-500",
    },
    {
      name: "Swift",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/swift/swift-original.svg",
      color: "from-orange-500 to-red-500",
    },
    {
      name: "Flutter",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/flutter/flutter-original.svg",
      color: "from-blue-400 to-cyan-400",
    },
    {
      name: "React Native",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/react/react-original.svg",
      color: "from-cyan-500 to-blue-500",
    },
    {
      name: "Firebase",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/firebase/firebase-original.svg",
      color: "from-yellow-400 to-orange-400",
    },
    {
      name: "Redux",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/redux/redux-original.svg",
      color: "from-purple-600 to-pink-600",
    },
    {
      name: "GraphQL",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/graphql/graphql-plain.svg",
      color: "from-pink-500 to-rose-500",
    },
    {
      name: "TypeScript",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/typescript/typescript-original.svg",
      color: "from-blue-600 to-indigo-600",
    },
    {
      name: "Node.js",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/nodejs/nodejs-original.svg",
      color: "from-green-500 to-emerald-500",
    },
    {
      name: "MongoDB",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/mongodb/mongodb-original.svg",
      color: "from-emerald-500 to-teal-500",
    },
    {
      name: "PostgreSQL",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/postgresql/postgresql-original.svg",
      color: "from-blue-500 to-indigo-500",
    },
    {
      name: "Git",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/git/git-original.svg",
      color: "from-orange-600 to-red-600",
    },
    {
      name: "Docker",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/docker/docker-original.svg",
      color: "from-blue-400 to-cyan-400",
    },
    {
      name: "AWS",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/amazonwebservices/amazonwebservices-original-wordmark.svg",
      color: "from-orange-500 to-yellow-500",
    },
    {
      name: "Android",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/android/android-original.svg",
      color: "from-green-400 to-emerald-400",
    },
    {
      name: "iOS",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/apple/apple-original.svg",
      color: "from-gray-600 to-gray-800",
    },
    {
      name: "VS Code",
      iconUrl:
        "https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/vscode/vscode-original.svg",
      color: "from-blue-500 to-cyan-500",
    },
  ];

  return (
    <section
      ref={sectionRef}
      id="skills"
      className="py-24 bg-gradient-to-br from-gray-50 to-white"
    >
      <div className="container mx-auto px-6">
        <div className="text-center mb-20 scroll-reveal">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Code2 size={16} />
            Technical Expertise
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            My <span className="text-gradient">Skills</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            A comprehensive showcase of technologies, frameworks, and tools I
            use to build exceptional mobile applications and deliver outstanding
            results.
          </p>
        </div>

        {/* Scrolling Skills Icons */}
        <div className="scroll-reveal mb-16">
          <div className="relative overflow-hidden">
            <div className="flex animate-scroll-left space-x-8 py-8">
              {[...allSkills, ...allSkills].map((skill, index) => {
                return (
                  <div key={index} className="flex-shrink-0 group">
                    <div className="relative">
                      <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-2xl transition-all duration-300 transform group-hover:scale-110 group-hover:-translate-y-2 p-3 border border-gray-200">
                        <img
                          src={skill.iconUrl}
                          alt={skill.name}
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-gray-900 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap">
                          {skill.name}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Reverse Direction Scroll */}
        <div className="scroll-reveal mb-16">
          <div className="relative overflow-hidden">
            <div className="flex animate-scroll-right space-x-8 py-8">
              {[
                ...allSkills.slice().reverse(),
                ...allSkills.slice().reverse(),
              ].map((skill, index) => {
                return (
                  <div key={index} className="flex-shrink-0 group">
                    <div className="relative">
                      <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-2xl transition-all duration-300 transform group-hover:scale-110 group-hover:-translate-y-2 p-3 border border-gray-200">
                        <img
                          src={skill.iconUrl}
                          alt={skill.name}
                          className="w-12 h-12 object-contain"
                        />
                      </div>
                      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-gray-900 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap">
                          {skill.name}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Domain Expertise */}
        <div className="scroll-reveal">
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              Domain <span className="text-gradient">Expertise</span>
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  name: "MedTech",
                  icon: Shield,
                  color: "from-green-500 to-emerald-500",
                },
                {
                  name: "Tourism",
                  icon: Globe,
                  color: "from-blue-500 to-cyan-500",
                },
                {
                  name: "Construction",
                  icon: Wrench,
                  color: "from-orange-500 to-red-500",
                },
                {
                  name: "Secure Systems",
                  icon: Shield,
                  color: "from-purple-500 to-indigo-500",
                },
              ].map((domain, index) => {
                const IconComponent = domain.icon;
                return (
                  <div
                    key={index}
                    className="text-center p-6 bg-gradient-to-br from-gray-50 to-white rounded-2xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100"
                  >
                    <div
                      className={`w-16 h-16 bg-gradient-to-br ${domain.color} rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg`}
                    >
                      <IconComponent className="text-white" size={24} />
                    </div>
                    <h4 className="font-semibold text-gray-900 text-lg">
                      {domain.name}
                    </h4>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
