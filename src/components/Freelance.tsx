import React, { useEffect, useRef, useState } from "react";
import { Star, Quote, Briefcase, Calendar, MapPin } from "lucide-react";
import { Card, CardContent } from "./ui/card";

const Freelance = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalProjects, setTotalProjects] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    setTotalProjects(freelanceJobs.length);
  }, []);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollLeft = container.scrollLeft;
      const containerWidth = container.clientWidth;
      const scrollWidth = container.scrollWidth;
      const scrollPercentage = scrollLeft / (scrollWidth - containerWidth);
      const newIndex = Math.round(scrollPercentage * (totalProjects - 2));
      setCurrentIndex(Math.max(0, Math.min(newIndex, totalProjects - 2)));
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [totalProjects]);

  const freelanceJobs = [
    {
      title: "Flutter Music App Bug Fixes & Store Distribution",
      client: "Music App Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Recent Project",
      role: "Flutter Developer",
      description:
        "Fixed critical bugs in a Flutter music application including song length issues with silence periods and background playback functionality on iPhone. Successfully prepared and distributed the app to both Play Store and App Store.",
      technologies: [
        "Flutter",
        "iOS Development",
        "Android Development",
        "App Store Distribution",
        "Play Store Distribution",
      ],
      achievements: [
        "Resolved song length issues with silence periods",
        "Fixed background music playback on iPhone",
        "Successfully distributed app to Play Store and App Store",
        "Improved overall app stability and performance",
        "Delivered within project timeline",
      ],
      feedback: {
        rating: 5,
        text: "The guy is hidden gem, he completely job successfully. Definitely recommend and will hire him again for future work.",
        client: "Abdurrahman Khan",
      },
    },
    {
      title: "Flutter API Integration Project",
      client: "Node.js API Client",
      platform: "Upwork",
      location: "Remote",
      duration: "14-Day Project",
      role: "Flutter Developer",
      description:
        "Integrated Node.js API into a Flutter application within a strict 14-day deadline. Focused on clean code architecture and efficient API communication patterns.",
      technologies: [
        "Flutter",
        "Node.js API",
        "REST API Integration",
        "HTTP Requests",
        "JSON Parsing",
      ],
      achievements: [
        "Successfully integrated Node.js API within 14-day deadline",
        "Implemented clean and maintainable code architecture",
        "Ensured efficient API communication patterns",
        "Delivered project within non-negotiable budget",
        "Maintained high code quality standards",
      ],
      feedback: {
        rating: 5,
        text: "Very efficient and writes clean code.",
        client: "Novus Tech",
      },
    },
    {
      title: "Full-Stack Mobile App Development",
      client: "Business Requirements Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Full-Stack Project",
      role: "iOS/Android Developer",
      description:
        "Developed a complete full-stack mobile application based on detailed business requirements document. Implemented the user interface from provided Figma designs with pixel-perfect accuracy.",
      technologies: [
        "Flutter",
        "Figma Design Implementation",
        "Full-Stack Development",
        "UI/UX Implementation",
        "Business Logic",
      ],
      achievements: [
        "Delivered complete full-stack mobile application",
        "Implemented pixel-perfect UI from Figma designs",
        "Met all business requirements specifications",
        "Ensured cross-platform compatibility",
        "Maintained high code quality standards",
      ],
      feedback: {
        rating: 5,
        text: "5 stars! Would highly recommend!",
        client: "Timothy McGinnis",
      },
    },
    {
      title: "Heart Rate Measurement App",
      client: "Health Tech Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Urgent Project",
      role: "Mobile Developer",
      description:
        "Implemented accurate heart rate measurement feature using phone camera technology. Integrated the functionality into an existing app with pre-designed UI components.",
      technologies: [
        "Flutter",
        "Camera API",
        "Heart Rate Detection",
        "Image Processing",
        "Health Monitoring",
      ],
      achievements: [
        "Successfully implemented camera-based heart rate measurement",
        "Integrated feature into existing app architecture",
        "Delivered urgent project within tight deadline",
        "Ensured accurate measurement algorithms",
        "Maintained existing UI design consistency",
      ],
      feedback: {
        rating: 5,
        text: "He is an excellent mobile app developer with advanced knowledge, and is very cooperative and dedicated to his work. I strongly advise you to hire this developer.",
        client: "Lusyo Tan",
      },
    },
    {
      title: "Image Editor Enhancement",
      client: "Image Processing Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Feature Enhancement",
      role: "Flutter Developer",
      description:
        "Enhanced image_editor_pro plugin to support image overlay functionality. Added capability for users to select images from camera or gallery, overlay them on main images, resize, and save results.",
      technologies: [
        "Flutter",
        "Image Editor Pro Plugin",
        "Camera Integration",
        "Gallery Access",
        "Image Processing",
      ],
      achievements: [
        "Extended plugin functionality for image overlay",
        "Implemented camera and gallery image selection",
        "Added image resizing and positioning features",
        "Maintained existing plugin architecture",
        "Delivered seamless user experience",
      ],
      feedback: {
        rating: 5,
        text: "He delivered good work on this project and I enjoyed working with him. His communication was top-notch, he met all deadlines, and his skills were reasonably strong. I enjoyed working with him and will likely have additional jobs for him in the future.",
        client: "Vidyut Shah",
      },
    },
    {
      title: "Flutter JSON Data App",
      client: "Data Management Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Urgent Requirement",
      role: "Flutter Developer",
      description:
        "Created a Flutter app using JSON file as mock API data with user authentication flow. Implemented local state management using Hive database for sign-in persistence.",
      technologies: [
        "Flutter",
        "Dart",
        "Hive Database",
        "GetStorage",
        "JSON Processing",
        "Local Storage",
      ],
      achievements: [
        "Built complete user authentication flow",
        "Implemented local state persistence with Hive",
        "Created responsive UI from provided designs",
        "Delivered within urgent timeline",
        "Ensured smooth user experience transitions",
      ],
      feedback: {
        rating: 5,
        text: "Abdul-Rahman Hany is a very honest person. Very cooperative and has a full 100% commitment to his work. I would recommend you to hire this developer especially for UI designing in Flutter and Firebase concepts.",
        client: "Anirudh Chawla",
      },
    },
    {
      title: "Quick UI Design Project",
      client: "Design Sprint Client",
      platform: "Upwork",
      location: "Remote",
      duration: "1 Hour Deadline",
      role: "UI Designer",
      description:
        "Designed 2 simple mobile app screens within an extremely tight 1-hour deadline. Delivered high-quality UI designs that met client specifications under pressure.",
      technologies: [
        "Flutter",
        "UI/UX Design",
        "Rapid Prototyping",
        "Mobile Design",
        "Quick Delivery",
      ],
      achievements: [
        "Delivered 2 complete screen designs in 1 hour",
        "Met all client design requirements",
        "Maintained high design quality under pressure",
        "Demonstrated exceptional time management",
        "Exceeded client expectations for speed",
      ],
      feedback: {
        rating: 5,
        text: "Abdul-Rahman Hany is a very honest person. Very cooperative and has a full 100% commitment to his work. I would recommend you to hire this developer especially for UI designing in Flutter and Firebase concepts.",
        client: "Design Sprint Client",
      },
    },
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }
      />
    ));
  };

  return (
    <section
      ref={sectionRef}
      id="freelance"
      className="py-24 bg-gradient-to-br from-purple-50 to-blue-50"
    >
      <div className="container mx-auto px-6">
        <div className="text-center mb-20 scroll-reveal">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium mb-6">
            <Briefcase size={16} />
            Freelance Portfolio
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Freelance <span className="text-gradient">Success Stories</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Delivering exceptional mobile solutions for clients worldwide. Each
            project represents a successful partnership built on technical
            excellence and client satisfaction.
          </p>
          <div className="w-24 h-1 gradient-primary mx-auto rounded-full mt-6"></div>

          {/* Scroll hint */}
          <div className="flex items-center justify-center gap-2 mt-8 text-gray-500">
            <span className="text-sm">
              Viewing 2 projects at a time • Scroll to see more
            </span>
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-gray-300 rounded-full animate-pulse"></div>
              <div
                className="w-2 h-2 bg-gray-300 rounded-full animate-pulse"
                style={{ animationDelay: "0.2s" }}
              ></div>
              <div
                className="w-2 h-2 bg-gray-300 rounded-full animate-pulse"
                style={{ animationDelay: "0.4s" }}
              ></div>
            </div>
          </div>
        </div>

        <div className="w-full mx-auto">
          {/* Horizontal scroll container */}
          <div className="relative">
            <div
              ref={scrollContainerRef}
              className="flex gap-6 overflow-x-auto pb-6 scrollbar-hide snap-x snap-mandatory px-6"
              style={{
                scrollSnapType: "x mandatory",
                scrollBehavior: "smooth",
              }}
            >
              {freelanceJobs.map((job, index) => (
                <Card
                  key={index}
                  className="scroll-reveal group flex-shrink-0 w-[calc(50%-12px)] min-w-[300px] overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white snap-start"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardContent className="p-6 h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 gradient-primary rounded-full"></div>
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                          {job.platform}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-gray-500 text-xs">
                        <Calendar size={12} />
                        {job.duration}
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {job.title}
                    </h3>

                    <div className="flex flex-col gap-1 mb-3">
                      <div className="flex items-center gap-2 text-purple-600 font-semibold text-sm">
                        <Briefcase size={14} />
                        {job.role}
                      </div>
                      <div className="flex items-center gap-2 text-gray-600 text-sm">
                        <MapPin size={14} />
                        {job.client}
                      </div>
                    </div>

                    <p className="text-gray-600 mb-4 leading-relaxed text-sm line-clamp-3">
                      {job.description}
                    </p>

                    <div className="mb-4 flex-1">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center gap-1">
                        <div className="w-1.5 h-1.5 gradient-primary rounded-full"></div>
                        Key Achievements
                      </h4>
                      <ul className="space-y-1">
                        {job.achievements.slice(0, 3).map((achievement, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <div className="w-1 h-1 gradient-primary rounded-full mt-1.5 flex-shrink-0"></div>
                            <span className="text-gray-600 text-xs leading-relaxed">
                              {achievement}
                            </span>
                          </li>
                        ))}
                        {job.achievements.length > 3 && (
                          <li className="text-xs text-gray-500 italic">
                            +{job.achievements.length - 3} more achievements
                          </li>
                        )}
                      </ul>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">
                        Technologies
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {job.technologies.slice(0, 4).map((tech, i) => (
                          <span
                            key={i}
                            className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                        {job.technologies.length > 4 && (
                          <span className="px-2 py-1 bg-gray-200 text-gray-600 rounded-full text-xs">
                            +{job.technologies.length - 4}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4 border border-gray-100 mt-auto">
                      <div className="flex items-center gap-2 mb-2">
                        <Quote className="text-purple-600" size={16} />
                        <span className="font-semibold text-gray-900 text-sm">
                          Client Feedback
                        </span>
                        <div className="flex gap-1 ml-auto">
                          {renderStars(job.feedback.rating)}
                        </div>
                      </div>
                      <p className="text-gray-700 italic leading-relaxed mb-2 text-xs line-clamp-3">
                        "{job.feedback.text}"
                      </p>
                      <p className="text-xs font-medium text-purple-600">
                        — {job.feedback.client}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Scroll Indicator */}
            <div className="flex items-center justify-center gap-4 mt-8">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {Math.min(currentIndex + 2, totalProjects)} of {totalProjects}{" "}
                  projects
                </span>
              </div>
              <div className="flex gap-2">
                {Array.from(
                  { length: Math.ceil(totalProjects / 2) },
                  (_, i) => (
                    <div
                      key={i}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        Math.floor(currentIndex / 2) === i
                          ? "bg-purple-600 w-6"
                          : "bg-gray-300"
                      }`}
                    />
                  )
                )}
              </div>
              <div className="text-xs text-gray-500">Scroll to explore →</div>
            </div>
          </div>

          <div className="scroll-reveal text-center mt-16">
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Ready to Work Together?
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Looking for a dedicated mobile developer for your next project?
                Let's discuss how I can help bring your mobile app vision to
                life with the same excellence and attention to detail that my
                previous clients have experienced.
              </p>
              <div className="flex flex-wrap gap-4 justify-center">
                <a
                  href="#contact"
                  className="px-8 py-3 gradient-primary text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Start Your Project
                </a>
                <a
                  href="https://www.upwork.com/freelancers/abdulrahman"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-8 py-3 bg-white border-2 border-gray-300 text-gray-700 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:border-gray-400"
                >
                  View Upwork Profile
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Freelance;
