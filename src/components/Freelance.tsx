import React, { useEffect, useRef, useState } from "react";
import { Star, Quote, Briefcase, Calendar, MapPin } from "lucide-react";

const Freelance = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalProjects, setTotalProjects] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elementsToAnimate =
      sectionRef.current?.querySelectorAll(".scroll-reveal");
    elementsToAnimate?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const freelanceJobs = [
    {
      title: "Flutter Music App Bug Fixes & Store Distribution",
      client: "Music App Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Recent Project",
      role: "Flutter Developer",
      description:
        "Fixed critical bugs in a Flutter music application including song length issues with silence periods and background playback functionality on iPhone. Successfully prepared and distributed the app to both Play Store and App Store.",
      technologies: [
        "Flutter",
        "iOS Development",
        "Android Development",
        "App Store Distribution",
        "Play Store Distribution",
      ],
      achievements: [
        "Resolved song length issues with silence periods",
        "Fixed background music playback on iPhone",
        "Successfully distributed app to Play Store and App Store",
        "Improved overall app stability and performance",
        "Delivered within project timeline",
      ],
      feedback: {
        rating: 5,
        text: "The guy is hidden gem, he completely job successfully. Definitely recommend and will hire him again for future work.",
        client: "Abdurrahman Khan",
      },
    },
    {
      title: "Flutter API Integration Project",
      client: "Node.js API Client",
      platform: "Upwork",
      location: "Remote",
      duration: "14-Day Project",
      role: "Flutter Developer",
      description:
        "Integrated Node.js API into a Flutter application within a strict 14-day deadline. Focused on clean code architecture and efficient API communication patterns.",
      technologies: [
        "Flutter",
        "Node.js API",
        "REST API Integration",
        "HTTP Requests",
        "JSON Parsing",
      ],
      achievements: [
        "Successfully integrated Node.js API within 14-day deadline",
        "Implemented clean and maintainable code architecture",
        "Ensured efficient API communication patterns",
        "Delivered project within non-negotiable budget",
        "Maintained high code quality standards",
      ],
      feedback: {
        rating: 5,
        text: "Very efficient and writes clean code.",
        client: "Novus Tech",
      },
    },
    {
      title: "Full-Stack Mobile App Development",
      client: "Business Requirements Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Full-Stack Project",
      role: "iOS/Android Developer",
      description:
        "Developed a complete full-stack mobile application based on detailed business requirements document. Implemented the user interface from provided Figma designs with pixel-perfect accuracy.",
      technologies: [
        "Flutter",
        "Figma Design Implementation",
        "Full-Stack Development",
        "UI/UX Implementation",
        "Business Logic",
      ],
      achievements: [
        "Delivered complete full-stack mobile application",
        "Implemented pixel-perfect UI from Figma designs",
        "Met all business requirements specifications",
        "Ensured cross-platform compatibility",
        "Maintained high code quality standards",
      ],
      feedback: {
        rating: 5,
        text: "5 stars! Would highly recommend!",
        client: "Timothy McGinnis",
      },
    },
    {
      title: "Heart Rate Measurement App",
      client: "Health Tech Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Urgent Project",
      role: "Mobile Developer",
      description:
        "Implemented accurate heart rate measurement feature using phone camera technology. Integrated the functionality into an existing app with pre-designed UI components.",
      technologies: [
        "Flutter",
        "Camera API",
        "Heart Rate Detection",
        "Image Processing",
        "Health Monitoring",
      ],
      achievements: [
        "Successfully implemented camera-based heart rate measurement",
        "Integrated feature into existing app architecture",
        "Delivered urgent project within tight deadline",
        "Ensured accurate measurement algorithms",
        "Maintained existing UI design consistency",
      ],
      feedback: {
        rating: 5,
        text: "He is an excellent mobile app developer with advanced knowledge, and is very cooperative and dedicated to his work. I strongly advise you to hire this developer.",
        client: "Lusyo Tan",
      },
    },
    {
      title: "Image Editor Enhancement",
      client: "Image Processing Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Feature Enhancement",
      role: "Flutter Developer",
      description:
        "Enhanced image_editor_pro plugin to support image overlay functionality. Added capability for users to select images from camera or gallery, overlay them on main images, resize, and save results.",
      technologies: [
        "Flutter",
        "Image Editor Pro Plugin",
        "Camera Integration",
        "Gallery Access",
        "Image Processing",
      ],
      achievements: [
        "Extended plugin functionality for image overlay",
        "Implemented camera and gallery image selection",
        "Added image resizing and positioning features",
        "Maintained existing plugin architecture",
        "Delivered seamless user experience",
      ],
      feedback: {
        rating: 5,
        text: "He delivered good work on this project and I enjoyed working with him. His communication was top-notch, he met all deadlines, and his skills were reasonably strong. I enjoyed working with him and will likely have additional jobs for him in the future.",
        client: "Vidyut Shah",
      },
    },
    {
      title: "Flutter JSON Data App",
      client: "Data Management Client",
      platform: "Upwork",
      location: "Remote",
      duration: "Urgent Requirement",
      role: "Flutter Developer",
      description:
        "Created a Flutter app using JSON file as mock API data with user authentication flow. Implemented local state management using Hive database for sign-in persistence.",
      technologies: [
        "Flutter",
        "Dart",
        "Hive Database",
        "GetStorage",
        "JSON Processing",
        "Local Storage",
      ],
      achievements: [
        "Built complete user authentication flow",
        "Implemented local state persistence with Hive",
        "Created responsive UI from provided designs",
        "Delivered within urgent timeline",
        "Ensured smooth user experience transitions",
      ],
      feedback: {
        rating: 5,
        text: "Abdul-Rahman Hany is a very honest person. Very cooperative and has a full 100% commitment to his work. I would recommend you to hire this developer especially for UI designing in Flutter and Firebase concepts.",
        client: "Anirudh Chawla",
      },
    },
    {
      title: "Quick UI Design Project",
      client: "Design Sprint Client",
      platform: "Upwork",
      location: "Remote",
      duration: "1 Hour Deadline",
      role: "UI Designer",
      description:
        "Designed 2 simple mobile app screens within an extremely tight 1-hour deadline. Delivered high-quality UI designs that met client specifications under pressure.",
      technologies: [
        "Flutter",
        "UI/UX Design",
        "Rapid Prototyping",
        "Mobile Design",
        "Quick Delivery",
      ],
      achievements: [
        "Delivered 2 complete screen designs in 1 hour",
        "Met all client design requirements",
        "Maintained high design quality under pressure",
        "Demonstrated exceptional time management",
        "Exceeded client expectations for speed",
      ],
      feedback: {
        rating: 5,
        text: "Abdul-Rahman Hany is a very honest person. Very cooperative and has a full 100% commitment to his work. I would recommend you to hire this developer especially for UI designing in Flutter and Firebase concepts.",
        client: "Design Sprint Client",
      },
    },
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }
      />
    ));
  };

  return (
    <section
      ref={sectionRef}
      id="freelance"
      className="py-20 bg-gray-900 text-white relative overflow-hidden"
    >
      {/* Modern background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-cyan-600/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-16 scroll-reveal">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white text-sm font-medium mb-8">
            <div className="w-2 h-2 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full animate-pulse"></div>
            Client Success Stories
            <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full animate-pulse"></div>
          </div>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white via-cyan-200 to-purple-200 bg-clip-text text-transparent">
              Real Projects,
            </span>
            <br />
            <span className="bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
              Real Results
            </span>
          </h2>

          <p className="text-lg text-white/70 max-w-2xl mx-auto leading-relaxed mb-8">
            8 successful projects • 5-star ratings • Global clients
          </p>

          {/* Modern scroll hint */}
          <div className="flex items-center justify-center gap-4 text-white/50">
            <div className="h-px w-12 bg-gradient-to-r from-transparent to-white/30"></div>
            <span className="text-sm font-medium">Swipe to explore</span>
            <div className="h-px w-12 bg-gradient-to-l from-transparent to-white/30"></div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto">
          {/* Horizontal scroll container */}
          <div className="relative">
            <div className="flex gap-6 overflow-x-auto pb-6 scrollbar-hide snap-x snap-mandatory">
              {freelanceJobs.map((job, index) => (
                <div
                  key={index}
                  className="scroll-reveal group flex-shrink-0 w-[calc(50%-12px)] min-w-[380px] max-w-[480px] snap-start"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="relative h-[650px] bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:bg-white/10 hover:border-white/20 transition-all duration-500 group-hover:scale-[1.02] overflow-hidden">
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Content */}
                    <div className="relative z-10 h-full flex flex-col">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full animate-pulse"></div>
                          <span className="px-3 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-400/30 text-cyan-300 rounded-xl text-xs font-medium backdrop-blur-sm">
                            {job.platform}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-white/50 text-xs">
                          <Calendar size={12} />
                          {job.duration}
                        </div>
                      </div>

                      {/* Title */}
                      <h3 className="text-xl font-bold text-white mb-4 line-clamp-2 leading-tight">
                        {job.title}
                      </h3>

                      {/* Meta info */}
                      <div className="flex flex-col gap-2 mb-4">
                        <div className="flex items-center gap-2 text-cyan-300 font-medium text-sm">
                          <Briefcase size={14} />
                          {job.role}
                        </div>
                        <div className="flex items-center gap-2 text-white/60 text-sm">
                          <MapPin size={14} />
                          {job.client}
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-white/70 mb-6 leading-relaxed text-sm line-clamp-3">
                        {job.description}
                      </p>

                      {/* Technologies */}
                      <div className="mb-6 flex-1">
                        <div className="flex flex-wrap gap-2">
                          {job.technologies.slice(0, 4).map((tech, i) => (
                            <span
                              key={i}
                              className="px-3 py-1 bg-white/10 border border-white/20 text-white/80 rounded-xl text-xs font-medium backdrop-blur-sm"
                            >
                              {tech}
                            </span>
                          ))}
                          {job.technologies.length > 4 && (
                            <span className="px-3 py-1 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-400/30 text-cyan-300 rounded-xl text-xs font-medium">
                              +{job.technologies.length - 4}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Client feedback */}
                      <div className="bg-white/5 border border-white/10 rounded-2xl p-4 mt-auto backdrop-blur-sm">
                        <div className="flex items-center gap-2 mb-3">
                          <Quote className="text-cyan-400" size={16} />
                          <div className="flex gap-1 ml-auto">
                            {renderStars(job.feedback.rating)}
                          </div>
                        </div>
                        <p className="text-white/80 italic leading-relaxed mb-3 text-sm line-clamp-2">
                          "{job.feedback.text}"
                        </p>
                        <p className="text-xs font-medium text-cyan-300">
                          — {job.feedback.client}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Modern Scroll Indicator */}
            <div className="flex items-center justify-center gap-6 mt-12">
              <div className="flex items-center gap-3">
                <span className="text-sm text-white/60 font-medium">
                  {Math.min(currentIndex + 2, totalProjects)} of {totalProjects}
                </span>
              </div>

              <div className="flex gap-2">
                {Array.from(
                  { length: Math.ceil(totalProjects / 2) },
                  (_, i) => (
                    <div
                      key={i}
                      className={`h-1 rounded-full transition-all duration-500 ${
                        Math.floor(currentIndex / 2) === i
                          ? "bg-gradient-to-r from-cyan-400 to-purple-400 w-8"
                          : "bg-white/20 w-2 hover:bg-white/30"
                      }`}
                    />
                  )
                )}
              </div>

              <div className="flex items-center gap-2 text-white/40">
                <div className="w-4 h-px bg-gradient-to-r from-transparent to-white/30"></div>
                <span className="text-xs font-medium">Drag</span>
                <div className="w-4 h-px bg-gradient-to-l from-transparent to-white/30"></div>
              </div>
            </div>
          </div>

          <div className="scroll-reveal text-center mt-20">
            <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 max-w-2xl mx-auto overflow-hidden">
              {/* Background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-600/10 to-purple-600/10"></div>

              <div className="relative z-10">
                <h3 className="text-3xl font-bold text-white mb-4">
                  Ready to Build Something
                  <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                    {" "}
                    Amazing?
                  </span>
                </h3>
                <p className="text-white/70 mb-8 leading-relaxed">
                  Join 8+ satisfied clients who've experienced exceptional
                  mobile development. Let's turn your vision into reality.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="#contact"
                    className="group px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25 relative overflow-hidden"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative z-10">Start Your Project</span>
                  </a>
                  <a
                    href="https://www.upwork.com/freelancers/abdulrahman"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-8 py-4 bg-white/10 border border-white/20 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 hover:bg-white/20 backdrop-blur-sm"
                  >
                    View Upwork Profile
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Freelance;
