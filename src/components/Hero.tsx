import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>D<PERSON> } from "lucide-react";

const Hero = () => {
  const scrollToAbout = () => {
    const aboutSection = document.getElementById("about");
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative overflow-hidden"
    >
      <div className="absolute inset-0 gradient-primary opacity-90"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
        <div
          className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full animate-float"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          <div className="lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0">
            <h1 className="text-5xl lg:text-7xl font-bold text-white mb-6 animate-fade-in-up">
              Abdul-Rahman
              <span className="block text-4xl lg:text-6xl mt-2">Hany</span>
            </h1>

            <p
              className="text-xl lg:text-2xl text-white/90 mb-8 animate-fade-in-up"
              style={{ animationDelay: "0.2s" }}
            >
              Mobile App Developer
            </p>

            <p
              className="text-lg text-white/80 mb-8 max-w-2xl animate-fade-in-up"
              style={{ animationDelay: "0.4s" }}
            >
              4+ years of experience building native iOS and Android
              applications using Flutter and React Native. Passionate about
              creating intuitive mobile experiences in MedTech, Tourism, and
              innovative domains.
            </p>

            <div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8 animate-fade-in-up"
              style={{ animationDelay: "0.6s" }}
            >
              <button className="px-8 py-4 bg-white text-purple-600 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                View My Work
              </button>
              <button className="px-8 py-4 border-2 border-white text-white rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300">
                Contact Me
              </button>
            </div>

            <div
              className="flex gap-6 justify-center lg:justify-start animate-fade-in-up"
              style={{ animationDelay: "0.8s" }}
            >
              <a
                href="https://www.linkedin.com/in/abdulrahmanhany93/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-gray-200 transition-colors duration-300 transform hover:scale-110"
              >
                <Linkedin size={28} />
              </a>
              <a
                href="https://github.com/bedivvv"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-gray-200 transition-colors duration-300 transform hover:scale-110"
              >
                <Github size={28} />
              </a>
            </div>
          </div>

          <div className="lg:w-1/2 flex justify-center animate-slide-in-right">
            <div className="relative">
              <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-8 border-white/20 shadow-2xl">
                <img
                  src="https://i.ibb.co/q3pQ6y84/IMG-0087-min.jpg"
                  alt="Abdul-Rahman Hany"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-secondary rounded-full animate-bounce-slow"></div>
              <div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-accent rounded-full animate-bounce-slow"
                style={{ animationDelay: "1s" }}
              ></div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <button
            onClick={scrollToAbout}
            className="text-white hover:text-gray-200 transition-colors duration-300"
          >
            <ArrowDown size={32} />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
