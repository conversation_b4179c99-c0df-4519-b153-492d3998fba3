import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>ed<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Zap } from "lucide-react";

const Hero = () => {
  const scrollToSkills = () => {
    const skillsSection = document.getElementById("skills");
    if (skillsSection) {
      skillsSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900"
    >
      {/* Modern gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 via-blue-600/20 to-cyan-600/20"></div>

      {/* Enhanced animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full animate-float backdrop-blur-sm"></div>
        <div
          className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-400/15 to-purple-400/15 rounded-full animate-float backdrop-blur-sm"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full animate-float backdrop-blur-sm"
          style={{ animationDelay: "4s" }}
        ></div>

        {/* Additional modern elements */}
        <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-white rounded-full animate-ping"></div>
        <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-cyan-400 rounded-full animate-pulse"></div>
        <div className="absolute top-2/3 right-1/4 w-3 h-3 bg-purple-400 rounded-full animate-bounce"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
          <div className="lg:w-1/2 text-center lg:text-left mb-8 lg:mb-0">
            {/* Modern badge */}
            <div className="inline-flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/90 text-xs sm:text-sm font-medium mb-4 sm:mb-6 animate-fade-in-up">
              <Sparkles size={14} className="text-cyan-400 sm:w-4 sm:h-4" />
              <span className="whitespace-nowrap">Available for Projects</span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-4 sm:mb-6 animate-fade-in-up leading-tight">
              <span className="bg-gradient-to-r from-white via-cyan-200 to-purple-200 bg-clip-text text-transparent">
                Abdul-Rahman
              </span>
              <span className="block text-3xl sm:text-4xl lg:text-6xl mt-1 sm:mt-2 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                Hany
              </span>
            </h1>

            <div
              className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3 justify-center lg:justify-start mb-4 sm:mb-6 animate-fade-in-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                <Code className="text-white" size={20} />
              </div>
              <p className="text-lg sm:text-xl lg:text-2xl text-white/90 font-semibold text-center sm:text-left">
                Mobile App Developer
              </p>
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-lg flex items-center justify-center">
                <Zap className="text-white" size={14} />
              </div>
            </div>

            <p
              className="text-base sm:text-lg text-white/80 mb-6 sm:mb-8 max-w-2xl animate-fade-in-up leading-relaxed text-center lg:text-left px-4 sm:px-0"
              style={{ animationDelay: "0.4s" }}
            >
              <strong className="text-cyan-300">4+ years</strong> of experience
              building exceptional mobile applications using
              <strong className="text-purple-300"> Flutter</strong> and{" "}
              <strong className="text-blue-300">React Native</strong>.
              Transforming ideas into powerful, user-friendly mobile experiences
              that users love.
            </p>

            <div
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start mb-6 sm:mb-8 animate-fade-in-up px-4 sm:px-0"
              style={{ animationDelay: "0.6s" }}
            >
              <button
                onClick={() => {
                  const projectsSection = document.getElementById("projects");
                  if (projectsSection) {
                    projectsSection.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-white to-gray-100 text-purple-600 rounded-2xl font-bold text-base sm:text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl overflow-hidden w-full sm:w-auto"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-100 to-purple-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">View My Work</span>
              </button>
              <button
                onClick={() => {
                  const contactSection = document.getElementById("contact");
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="group px-6 sm:px-8 py-3 sm:py-4 border-2 border-white/30 bg-white/10 backdrop-blur-sm text-white rounded-2xl font-bold text-base sm:text-lg hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl w-full sm:w-auto"
              >
                Let's Connect
              </button>
            </div>

            <div
              className="flex gap-3 sm:gap-4 justify-center lg:justify-start animate-fade-in-up"
              style={{ animationDelay: "0.8s" }}
            >
              <a
                href="https://www.linkedin.com/in/abdulrahmanhany93/"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-10 h-10 sm:w-12 sm:h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl flex items-center justify-center text-white hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-110 hover:rotate-12"
              >
                <Linkedin size={18} className="sm:w-5 sm:h-5" />
              </a>
              <a
                href="https://github.com/bedivvv"
                target="_blank"
                rel="noopener noreferrer"
                className="group w-10 h-10 sm:w-12 sm:h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl flex items-center justify-center text-white hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-110 hover:rotate-12"
              >
                <Github size={18} className="sm:w-5 sm:h-5" />
              </a>
            </div>
          </div>

          <div className="lg:w-1/2 flex justify-center animate-slide-in-right">
            <div className="relative">
              <div className="w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 sm:border-8 border-white/20 shadow-2xl">
                <img
                  src="https://i.ibb.co/q3pQ6y84/IMG-0087-min.jpg"
                  alt="Abdul-Rahman Hany"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 w-12 h-12 sm:w-20 sm:h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full animate-bounce-slow"></div>
              <div
                className="absolute -bottom-2 -left-2 sm:-bottom-4 sm:-left-4 w-10 h-10 sm:w-16 sm:h-16 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-full animate-bounce-slow"
                style={{ animationDelay: "1s" }}
              ></div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <button
            onClick={scrollToSkills}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-110"
          >
            <ArrowDown size={16} className="sm:w-5 sm:h-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
